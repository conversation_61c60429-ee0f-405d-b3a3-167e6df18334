# AOD Calculation Module - Simplified Version

using CSV
using DataFrames

"""
    calculate_aod_profile(beta_aerosol::Vector{Float64}, altitude::Vector{Float64}) -> Vector{Float64}

Calculate AOD profile from aerosol backscatter.
"""
function calculate_aod_profile(beta_aerosol::Vector{Float64}, altitude::Vector{Float64})
    lidar_ratio = 50.0  # sr (typical value)
    
    aod_profile = zeros(length(beta_aerosol))
    
    for i in 2:length(beta_aerosol)
        dz = altitude[i] - altitude[i-1]
        avg_beta = (beta_aerosol[i-1] + beta_aerosol[i]) / 2
        aod_profile[i] = aod_profile[i-1] + lidar_ratio * avg_beta * dz
    end
    
    return aod_profile
end

"""
    process_aod_calculation_csv(input_csv::String, output_csv::String="") -> String

Process AOD calculation for CSV file.
"""
function process_aod_calculation_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_aod.csv")
    end
    
    println("Processing AOD calculation: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Beta_aerosol" in names(df)) || !("Range_m" in names(df))
        error("Required columns not found. Run backscatter analysis first.")
    end
    
    beta_aerosol = df.Beta_aerosol
    altitude = df.Range_m
    
    aod_profile = calculate_aod_profile(beta_aerosol, altitude)
    
    # Add AOD data
    df.AOD_profile = aod_profile
    df.AOD_total = fill(aod_profile[end], nrow(df))
    
    println("📊 Total AOD: ", round(aod_profile[end], digits=6))
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ AOD calculation data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_aod_calculation(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process AOD calculation.
"""
function batch_process_aod_calculation(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing AOD calculation...")
    
    csv_files = filter(file -> endswith(file, "_asr.csv"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No ASR CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), "_asr.csv" => "_aod.csv"))
            result_file = process_aod_calculation_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export calculate_aod_profile
export process_aod_calculation_csv, batch_process_aod_calculation
