# ASR Analysis Module - Simplified Version

using CSV
using DataFrames

"""
    calculate_asr(beta_total::Vector{Float64}, beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate Aerosol Scattering Ratio (ASR).
"""
function calculate_asr(beta_total::Vector{Float64}, beta_molecular::Vector{Float64})
    asr = beta_total ./ beta_molecular
    asr = max.(asr, 1.0)  # ASR should be >= 1
    
    return asr
end

"""
    process_asr_analysis_csv(input_csv::String, output_csv::String="") -> String

Process ASR analysis for CSV file.
"""
function process_asr_analysis_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_asr.csv")
    end
    
    println("Processing ASR analysis: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Beta_total" in names(df)) || !("Beta_molecular" in names(df))
        error("Required columns not found. Run backscatter analysis first.")
    end
    
    beta_total = df.Beta_total
    beta_molecular = df.Beta_molecular
    
    asr = calculate_asr(beta_total, beta_molecular)
    
    # Add ASR data
    df.ASR = asr
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ ASR analysis data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_asr_analysis(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process ASR analysis.
"""
function batch_process_asr_analysis(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing ASR analysis...")
    
    csv_files = filter(file -> endswith(file, "_backscatter.csv"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No backscatter CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), "_backscatter.csv" => "_asr.csv"))
            result_file = process_asr_analysis_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export calculate_asr
export process_asr_analysis_csv, batch_process_asr_analysis
