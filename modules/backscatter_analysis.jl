# Backscatter Analysis Module - Simplified Version

using CSV
using DataFrames

"""
    calculate_total_backscatter(normalized::Vector{Float64}, beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate total backscatter coefficient.
"""
function calculate_total_backscatter(normalized::Vector{Float64}, beta_molecular::Vector{Float64})
    # Simple calibration constant (should be determined from calibration)
    calibration_constant = 1e-6
    
    beta_total = normalized .* beta_molecular .* calibration_constant
    
    return beta_total
end

"""
    calculate_aerosol_backscatter(beta_total::Vector{Float64}, beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate aerosol backscatter coefficient.
"""
function calculate_aerosol_backscatter(beta_total::Vector{Float64}, beta_molecular::Vector{Float64})
    beta_aerosol = beta_total .- beta_molecular
    beta_aerosol = max.(beta_aerosol, 0.0)  # Ensure non-negative
    
    return beta_aerosol
end

"""
    process_backscatter_analysis_csv(input_csv::String, output_csv::String="") -> String

Process backscatter analysis for CSV file.
"""
function process_backscatter_analysis_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_backscatter.csv")
    end
    
    println("Processing backscatter analysis: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Normalized" in names(df)) || !("Beta_molecular" in names(df))
        error("Required columns not found. Run previous processing steps first.")
    end
    
    normalized = df.Normalized
    beta_molecular = df.Beta_molecular
    
    beta_total = calculate_total_backscatter(normalized, beta_molecular)
    beta_aerosol = calculate_aerosol_backscatter(beta_total, beta_molecular)
    
    # Add backscatter data
    df.Beta_total = beta_total
    df.Beta_aerosol = beta_aerosol
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ Backscatter analysis data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_backscatter_analysis(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process backscatter analysis.
"""
function batch_process_backscatter_analysis(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing backscatter analysis...")
    
    csv_files = filter(file -> endswith(file, "_molecular.csv"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No molecular CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), "_molecular.csv" => "_backscatter.csv"))
            result_file = process_backscatter_analysis_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export calculate_total_backscatter, calculate_aerosol_backscatter
export process_backscatter_analysis_csv, batch_process_backscatter_analysis
